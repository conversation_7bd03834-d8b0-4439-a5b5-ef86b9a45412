-- Migration: Add conversation_number columns to support conversation numbering
-- This migration adds conversation_number columns to conversation_history and conversation_state tables
-- to track conversation sequences for analytics while preserving conversation history.

-- Add conversation_number column to conversation_history table
ALTER TABLE conversation_history 
ADD COLUMN IF NOT EXISTS conversation_number INTEGER NOT NULL DEFAULT 1;

-- Add conversation_number column to conversation_state table  
ALTER TABLE conversation_state 
ADD COLUMN IF NOT EXISTS conversation_number INTEGER NOT NULL DEFAULT 1;

-- Update the unique constraint on conversation_state to include conversation_number
-- First drop the old constraint
ALTER TABLE conversation_state 
DROP CONSTRAINT IF EXISTS conversation_state_chat_id_bot_id_key;

-- Add new unique constraint including conversation_number
ALTER TABLE conversation_state 
ADD CONSTRAINT conversation_state_chat_id_bot_id_conversation_number_key 
UNIQUE (chat_id, bot_id, conversation_number);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_conversation_history_chat_conversation 
ON conversation_history(chat_id, conversation_number);

CREATE INDEX IF NOT EXISTS idx_conversation_state_chat_conversation 
ON conversation_state(chat_id, conversation_number);

-- Update any existing records to have conversation_number = 1
UPDATE conversation_history SET conversation_number = 1 WHERE conversation_number IS NULL;
UPDATE conversation_state SET conversation_number = 1 WHERE conversation_number IS NULL;
